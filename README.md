# 智能石头剪刀布游戏 🎮

一个能够学习玩家出招模式并制定对策的智能石头剪刀布游戏。

## 功能特点 ✨

- **智能AI对手**: AI会分析你的历史出招模式，预测你的下一步并选择最优对策
- **公平游戏机制**: AI先生成选择并创建加密承诺，确保不能作弊
- **多种分析算法**:
  - 频率分析：统计你最常用的选择
  - 模式匹配：识别你的出招序列模式
  - 序列分析：分析选择之间的转换规律
  - 反频率预测：预测你会避免常用选择
  - 反模式预测：预测你会故意打破模式

- **详细统计**: 游戏结束后显示胜率、选择偏好等统计信息

## 游戏规则 📋

- 石头砸剪刀 🪨 ✂️
- 剪刀剪布 ✂️ 📄  
- 布包石头 📄 🪨

## 如何运行 🚀

```bash
python3 rock_paper_scissors.py
```

## 游戏流程 🎯

1. **选择轮数**: 程序开始时选择要玩几局
2. **AI生成选择**: AI先分析你的历史模式并生成选择（加密存储）
3. **玩家输入**: 你输入自己的选择（石头/剪刀/布）
4. **揭晓答案**: 同时显示双方选择和验证信息
5. **统计更新**: 更新历史记录和分析数据
6. **最终统计**: 游戏结束后显示详细统计信息

## AI策略说明 🤖

AI使用多种算法来预测你的下一步：

1. **频率分析**: 如果你经常出石头，AI可能会出布
2. **模式识别**: 如果你有固定的出招序列，AI会识别并预测
3. **序列分析**: 分析你在某个选择后通常会出什么
4. **心理博弈**: 考虑你可能会故意避免常用选择或打破模式

## 示例输出 📊

```
=== 第 1 轮 ===
AI已经做出选择并生成承诺: a1b2c3d4e5f6g7h8...
请输入你的选择 (石头/剪刀/布): 石头

揭晓答案：
你的选择: 石头
AI的选择: 布
验证字符串: 布_1699123456.789_0.123456789
😔 你输了！

当前比分 - 你: 0, AI: 1
```

## 技术特点 🔧

- **加密承诺**: 使用SHA-256哈希确保AI不能在看到你的选择后修改自己的选择
- **模式学习**: 动态学习和适应玩家的策略
- **多层预测**: 结合多种算法提高预测准确率
- **统计分析**: 提供详细的游戏数据分析

## 挑战AI 💪

试试看你能否找到AI无法预测的策略！AI会越来越了解你的习惯，所以保持随机性是关键。

祝你游戏愉快！🎉
