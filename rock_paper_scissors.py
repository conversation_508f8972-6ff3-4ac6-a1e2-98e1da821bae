#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能石头剪刀布游戏
通过分析玩家历史出招模式来预测下一步并制定对策
"""

import random
import hashlib
import time
from collections import Counter, defaultdict
from typing import List, Dict, Tuple

class RockPaperScissorsAI:
    def __init__(self):
        self.choices = ['石头', '剪刀', '布']
        self.choice_map = {'石头': 0, '剪刀': 1, '布': 2}
        self.reverse_map = {0: '石头', 1: '剪刀', 2: '布'}
        
        # 游戏历史记录
        self.player_history = []
        self.ai_history = []
        self.results = []  # 'win', 'lose', 'tie'
        
        # 分析数据
        self.pattern_memory = defaultdict(list)  # 记录模式 -> 下一步的映射
        self.frequency_analysis = Counter()  # 频率分析
        self.sequence_analysis = defaultdict(Counter)  # 序列分析
        
        # 游戏设置
        self.total_rounds = 0
        self.current_round = 0
        
    def get_game_rounds(self) -> int:
        """获取游戏轮数"""
        while True:
            try:
                rounds = int(input("请输入要玩几局游戏: "))
                if rounds > 0:
                    return rounds
                else:
                    print("请输入大于0的数字！")
            except ValueError:
                print("请输入有效的数字！")
    
    def generate_secret_choice(self) -> str:
        """生成AI的秘密选择（加密存储）"""
        if len(self.player_history) < 2:
            # 前两轮随机选择
            choice = random.choice(self.choices)
        else:
            # 使用智能算法预测
            choice = self.predict_and_counter()
        
        # 生成时间戳和随机数，确保不可预测
        timestamp = str(time.time())
        random_salt = str(random.random())
        choice_with_salt = f"{choice}_{timestamp}_{random_salt}"
        
        # 生成哈希值作为承诺
        commitment = hashlib.sha256(choice_with_salt.encode()).hexdigest()
        
        return choice, commitment, choice_with_salt
    
    def predict_and_counter(self) -> str:
        """智能预测算法"""
        predictions = []
        
        # 1. 频率分析预测
        if self.player_history:
            most_common = self.frequency_analysis.most_common(1)[0][0]
            predictions.append(most_common)
        
        # 2. 模式匹配预测
        pattern_prediction = self.pattern_matching_prediction()
        if pattern_prediction:
            predictions.append(pattern_prediction)
        
        # 3. 序列分析预测
        sequence_prediction = self.sequence_analysis_prediction()
        if sequence_prediction:
            predictions.append(sequence_prediction)
        
        # 4. 反频率预测（假设玩家会避免最常用的）
        if len(self.frequency_analysis) > 1:
            least_common = self.frequency_analysis.most_common()[-1][0]
            predictions.append(least_common)
        
        # 5. 反模式预测（玩家可能会故意打破模式）
        if len(self.player_history) >= 2:
            last_choice = self.player_history[-1]
            # 预测玩家会选择与上次不同的
            other_choices = [c for c in self.choices if c != last_choice]
            predictions.extend(other_choices)
        
        # 综合预测结果
        if predictions:
            prediction_counter = Counter(predictions)
            predicted_choice = prediction_counter.most_common(1)[0][0]
        else:
            predicted_choice = random.choice(self.choices)
        
        # 返回克制预测选择的选项
        return self.get_counter_choice(predicted_choice)
    
    def pattern_matching_prediction(self) -> str:
        """基于历史模式匹配进行预测"""
        if len(self.player_history) < 3:
            return None
        
        # 寻找最近的模式
        for pattern_length in range(min(5, len(self.player_history)), 0, -1):
            current_pattern = tuple(self.player_history[-pattern_length:])
            if current_pattern in self.pattern_memory:
                next_choices = self.pattern_memory[current_pattern]
                if next_choices:
                    return Counter(next_choices).most_common(1)[0][0]
        
        return None
    
    def sequence_analysis_prediction(self) -> str:
        """基于序列分析进行预测"""
        if len(self.player_history) < 2:
            return None
        
        last_choice = self.player_history[-1]
        if last_choice in self.sequence_analysis:
            next_choice_counter = self.sequence_analysis[last_choice]
            if next_choice_counter:
                return next_choice_counter.most_common(1)[0][0]
        
        return None
    
    def get_counter_choice(self, predicted_choice: str) -> str:
        """获取克制预测选择的选项"""
        counter_map = {
            '石头': '布',    # 布包石头
            '剪刀': '石头',  # 石头砸剪刀
            '布': '剪刀'     # 剪刀剪布
        }
        return counter_map[predicted_choice]
    
    def get_player_choice(self) -> str:
        """获取玩家选择"""
        while True:
            choice = input(f"第{self.current_round + 1}轮 - 请输入你的选择 (石头/剪刀/布): ").strip()
            if choice in self.choices:
                return choice
            else:
                print("请输入有效的选择：石头、剪刀或布")
    
    def determine_winner(self, player_choice: str, ai_choice: str) -> str:
        """判断胜负"""
        if player_choice == ai_choice:
            return 'tie'
        
        win_conditions = {
            ('石头', '剪刀'): True,  # 石头砸剪刀
            ('剪刀', '布'): True,    # 剪刀剪布
            ('布', '石头'): True     # 布包石头
        }
        
        if (player_choice, ai_choice) in win_conditions:
            return 'win'
        else:
            return 'lose'
    
    def update_analysis_data(self, player_choice: str):
        """更新分析数据"""
        # 更新频率分析
        self.frequency_analysis[player_choice] += 1
        
        # 更新模式记忆
        if len(self.player_history) >= 1:
            for pattern_length in range(1, min(6, len(self.player_history) + 1)):
                if len(self.player_history) >= pattern_length:
                    pattern = tuple(self.player_history[-pattern_length:])
                    self.pattern_memory[pattern].append(player_choice)
        
        # 更新序列分析
        if len(self.player_history) >= 1:
            last_choice = self.player_history[-1]
            self.sequence_analysis[last_choice][player_choice] += 1
    
    def play_round(self, ai_choice: str, commitment: str, choice_with_salt: str):
        """进行一轮游戏"""
        print(f"\n=== 第 {self.current_round + 1} 轮 ===")
        print(f"AI已经做出选择并生成承诺: {commitment[:16]}...")
        
        # 获取玩家选择
        player_choice = self.get_player_choice()
        
        # 验证AI选择的完整性
        expected_commitment = hashlib.sha256(choice_with_salt.encode()).hexdigest()
        if commitment != expected_commitment:
            print("错误：AI选择验证失败！")
            return
        
        # 揭晓结果
        print(f"\n揭晓答案：")
        print(f"你的选择: {player_choice}")
        print(f"AI的选择: {ai_choice}")
        print(f"验证字符串: {choice_with_salt}")
        
        # 判断胜负
        result = self.determine_winner(player_choice, ai_choice)
        if result == 'win':
            print("🎉 你赢了！")
        elif result == 'lose':
            print("😔 你输了！")
        else:
            print("🤝 平局！")
        
        # 更新历史记录
        self.player_history.append(player_choice)
        self.ai_history.append(ai_choice)
        self.results.append(result)
        
        # 更新分析数据
        self.update_analysis_data(player_choice)
        
        self.current_round += 1
    
    def show_statistics(self):
        """显示游戏统计"""
        print(f"\n{'='*50}")
        print("🎮 游戏统计")
        print(f"{'='*50}")
        
        # 胜负统计
        wins = self.results.count('win')
        losses = self.results.count('lose')
        ties = self.results.count('tie')
        
        print(f"总轮数: {len(self.results)}")
        print(f"你的战绩: {wins}胜 {losses}负 {ties}平")
        print(f"你的胜率: {wins/len(self.results)*100:.1f}%")
        print(f"AI的胜率: {losses/len(self.results)*100:.1f}%")
        
        # 选择偏好统计
        print(f"\n📊 选择偏好分析:")
        player_counter = Counter(self.player_history)
        ai_counter = Counter(self.ai_history)
        
        print("你的选择统计:")
        for choice, count in player_counter.most_common():
            percentage = count / len(self.player_history) * 100
            print(f"  {choice}: {count}次 ({percentage:.1f}%)")
        
        print("AI的选择统计:")
        for choice, count in ai_counter.most_common():
            percentage = count / len(self.ai_history) * 100
            print(f"  {choice}: {count}次 ({percentage:.1f}%)")
        
        # 最喜欢的选择
        if player_counter:
            favorite_player = player_counter.most_common(1)[0][0]
            print(f"\n🎯 你最喜欢用: {favorite_player}")
        
        if ai_counter:
            favorite_ai = ai_counter.most_common(1)[0][0]
            print(f"🤖 AI最常用: {favorite_ai}")
    
    def play_game(self):
        """开始游戏"""
        print("🎮 欢迎来到智能石头剪刀布游戏！")
        print("这个AI会学习你的出招模式并尝试击败你！")
        print("游戏规则：石头砸剪刀，剪刀剪布，布包石头")
        
        self.total_rounds = self.get_game_rounds()
        
        for round_num in range(self.total_rounds):
            # AI先生成选择
            ai_choice, commitment, choice_with_salt = self.generate_secret_choice()
            
            # 进行游戏
            self.play_round(ai_choice, commitment, choice_with_salt)
            
            # 显示当前统计（除了最后一轮）
            if round_num < self.total_rounds - 1:
                wins = self.results.count('win')
                losses = self.results.count('lose')
                print(f"当前比分 - 你: {wins}, AI: {losses}")
        
        # 显示最终统计
        self.show_statistics()

def main():
    game = RockPaperScissorsAI()
    game.play_game()

if __name__ == "__main__":
    main()
